#!/usr/bin/env python3
"""
ComfyUI WebSocket 使用示例

这个示例展示了如何使用修复后的 ComfyUI 客户端，
它现在使用 WebSocket 进行实时通信，符合官方最佳实践。
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from core.services.comfyui import ComfyUIClient

def example_basic_usage():
    """基本使用示例"""
    
    print("=== ComfyUI WebSocket 基本使用示例 ===")
    
    # 创建客户端
    client = ComfyUIClient("http://127.0.0.1:8188")
    
    # 获取默认工作流
    workflows = client.get_default_workflows()
    text_to_image_workflow = workflows["text_to_image"]
    
    # 设置参数
    parameters = {
        "positive_prompt": "a beautiful landscape, masterpiece, best quality",
        "negative_prompt": "blurry, low quality",
        "width": 512,
        "height": 512,
        "num_images": 1
    }
    
    # 更新工作流参数
    updated_workflow = client.update_workflow_parameters(text_to_image_workflow, parameters)
    
    print("工作流参数已更新")
    
    # 方法 1: 使用新的 WebSocket 方法（推荐）
    print("\n使用 WebSocket 方法执行工作流...")
    try:
        success, images = client.run_workflow_with_websocket(updated_workflow, timeout=300)
        
        if success:
            print(f"✓ 工作流执行成功，生成了 {len(images)} 张图像")
            for i, image_data in enumerate(images):
                print(f"  图像 {i+1}: {len(image_data)} 字节")
        else:
            print("✗ 工作流执行失败")
            
    except Exception as e:
        print(f"✗ 执行出错: {e}")
    
    # 方法 2: 使用传统方法（向后兼容）
    print("\n使用传统方法执行工作流...")
    try:
        success, images = client.run_workflow(updated_workflow, timeout=300)
        
        if success:
            print(f"✓ 工作流执行成功，生成了 {len(images)} 张图像")
        else:
            print("✗ 工作流执行失败")
            
    except Exception as e:
        print(f"✗ 执行出错: {e}")

def example_advanced_usage():
    """高级使用示例"""
    
    print("\n=== ComfyUI WebSocket 高级使用示例 ===")
    
    client = ComfyUIClient("http://127.0.0.1:8188")
    
    # 分步执行：先提交 prompt，然后监听
    print("\n分步执行示例...")
    
    try:
        # 获取工作流
        workflow = client.get_default_workflows()["text_to_image"]
        
        # 提交 prompt
        prompt_id = client.queue_prompt(workflow)
        print(f"Prompt 已提交，ID: {prompt_id}")
        
        # 使用 WebSocket 等待完成
        success, images = client.wait_for_prompt(prompt_id, timeout=300)
        
        if success:
            print(f"✓ Prompt {prompt_id} 执行成功")
            
            # 获取特定 prompt 的历史记录
            history = client.get_history(prompt_id)
            print(f"历史记录获取成功: {prompt_id in history}")
            
        else:
            print(f"✗ Prompt {prompt_id} 执行失败")
            
    except Exception as e:
        print(f"✗ 高级使用出错: {e}")

def example_error_handling():
    """错误处理示例"""
    
    print("\n=== 错误处理示例 ===")
    
    # 测试连接错误
    client = ComfyUIClient("http://127.0.0.1:9999")  # 错误的端口
    
    try:
        workflow = {"test": "workflow"}
        success, images = client.run_workflow_with_websocket(workflow, timeout=5)
        print(f"结果: success={success}, images={len(images) if images else 0}")
        
    except Exception as e:
        print(f"✓ 正确捕获连接错误: {e}")

def show_websocket_advantages():
    """展示 WebSocket 的优势"""
    
    print("\n=== WebSocket vs HTTP 轮询对比 ===")
    
    print("🚀 WebSocket 优势:")
    print("1. 实时通信 - 立即接收执行状态更新")
    print("2. 低延迟 - 无需等待轮询间隔")
    print("3. 低开销 - 减少 HTTP 请求数量")
    print("4. 进度监控 - 实时接收执行进度")
    print("5. 官方推荐 - 符合 ComfyUI 最佳实践")
    
    print("\n📊 性能对比:")
    print("HTTP 轮询:")
    print("  - 每秒 1 次请求 = 300 次请求（5分钟）")
    print("  - 延迟: 0.5-1 秒")
    print("  - 网络开销: 高")
    
    print("WebSocket:")
    print("  - 1 次连接 + 实时消息")
    print("  - 延迟: < 100ms")
    print("  - 网络开销: 低")

def main():
    """主函数"""
    
    print("ComfyUI WebSocket 使用示例")
    print("=" * 50)
    
    # 检查是否有 ComfyUI 服务运行
    client = ComfyUIClient()
    try:
        # 尝试获取历史记录来测试连接
        client.get_history()
        print("✓ ComfyUI 服务连接正常")
        
        # 运行示例
        example_basic_usage()
        example_advanced_usage()
        
    except Exception as e:
        print(f"⚠ ComfyUI 服务未运行或连接失败: {e}")
        print("以下是使用示例（需要 ComfyUI 服务运行）:")
        
        # 显示示例代码
        show_websocket_advantages()
    
    # 错误处理示例（不需要服务运行）
    example_error_handling()
    
    print("\n🎯 总结:")
    print("- 使用 run_workflow_with_websocket() 获得最佳性能")
    print("- 原有的 run_workflow() 方法仍然可用（向后兼容）")
    print("- WebSocket 连接自动管理，无需手动处理")
    print("- 支持实时进度监控和错误处理")

if __name__ == "__main__":
    main()
